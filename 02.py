import pandas as pd
import numpy as np
import math
import matplotlib.pyplot as plt
from matplotlib.ticker import Func<PERSON><PERSON>atter

def calculate_allocations(df_m, df_n):
    """
    将df_m中的q_tau值按照物理模型分配给df_n的每一行
    
    参数:
    df_m : DataFrame - 源数据 (m行)，包含列: ['q_tau', '实时密度']
    df_n : DataFrame - 目标数据 (n行)，包含列: ['井深', '压力系数', '内聚力', '内摩擦角', '弹性模量']
    
    返回:
    DataFrame - 添加了分配值的新df_n
    """
    # 复制数据避免修改原始DataFrame
    df_n = df_n.copy()
    
    # 验证输入数据
    required_m_columns = ['q_tau', '实时密度']
    required_n_columns = ['井深', '压力系数', '内聚力', '内摩擦角', '弹性模量']
    
    for col in required_m_columns:
        if col not in df_m.columns:
            raise ValueError(f"df_m缺少必要列: {col}")
    
    for col in required_n_columns:
        if col not in df_n.columns:
            raise ValueError(f"df_n缺少必要列: {col}")
    
    if len(df_m) != 1:
        raise ValueError("df_m应该只包含一行数据")
    
    # 提取m行数据
    q_tau_total = df_m['q_tau'].iloc[0]
    density_m = df_m['实时密度'].iloc[0]
    
    # 预计算中间变量
    k_values = []  # 存储每行的k值
    M_sigma_values = []  # 存储每行的M(σ₃)值
    sigma_3_values = []  # 存储每行的σ₃值
    
    # 遍历df_n的每一行
    for idx, row in df_n.iterrows():
        depth = row['井深']
        pressure_coeff = row['压力系数']
        cohesion = row['内聚力']
        friction_angle = row['内摩擦角']
        elastic_modulus = row['弹性模量']
        
        # 计算σ₃ = P_w - P_o
        P_w = depth * density_m     # 水压
        P_o = pressure_coeff * depth  # 油压
        sigma_3 = P_w - P_o
        sigma_3_values.append(sigma_3)
        
        # 将内摩擦角转换为弧度
        phi_rad = np.radians(friction_angle)
        sin_phi = np.sin(phi_rad)
        cos_phi = np.cos(phi_rad)
        
        # 计算M(σ₃)
        numerator = 1 + sin_phi
        denominator = 1 - sin_phi
        
        # 避免除零错误
        if abs(denominator) < 1e-10:
            M_sigma = 0.0
        else:
            term1 = (numerator / denominator) * sigma_3
            term2 = (2 * cohesion * cos_phi) / denominator
            M_sigma = term1 + term2
        M_sigma_values.append(M_sigma)
        
        # 计算k值
        if abs(M_sigma) < 1e-10:
            k = 0.0
        else:
            k = (2 * elastic_modulus) / (M_sigma ** 2)
        
        k_values.append(k)
    
    # 计算归一化权重
    total_k = sum(k_values)
    
    # 分配q_tau值
    if total_k > 0:
        allocations = [(k / total_k) * q_tau_total for k in k_values]
    else:
        allocations = [0] * len(df_n)
    
    # 添加分配结果到df_n
    df_n['分配值'] = allocations
    df_n['分配权重'] = [k / total_k if total_k > 0 else 0 for k in k_values]
    df_n['σ₃'] = sigma_3_values
    df_n['M(σ₃)'] = M_sigma_values
    
    return df_n, q_tau_total

def plot_allocation_results(result_df, q_tau_total, save_path=None):
    """
    可视化分配结果
    
    参数:
    result_df : DataFrame - 包含分配结果的数据框
    q_tau_total : float - 原始待分配的总值
    save_path : str - 图片保存路径（可选）
    """
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    fig.suptitle(f'分配结果可视化 (总值: {q_tau_total:.2f})', fontsize=16)
    
    # 1. 柱状图 - 分配值
    depths = result_df['井深'].astype(str)
    allocations = result_df['分配值']
    
    bars = ax1.bar(depths, allocations, color='steelblue')
    ax1.set_title('按井深分配值', fontsize=14)
    ax1.set_xlabel('井深 (米)', fontsize=12)
    ax1.set_ylabel('分配值', fontsize=12)
    ax1.grid(axis='y', linestyle='--', alpha=0.7)
    
    # 在柱子上方添加数值标签
    for bar in bars:
        height = bar.get_height()
        ax1.annotate(f'{height:.2f}',
                     xy=(bar.get_x() + bar.get_width() / 2, height),
                     xytext=(0, 3),  # 3 points vertical offset
                     textcoords="offset points",
                     ha='center', va='bottom', fontsize=10)
    
    # 2. 饼图 - 分配权重
    labels = [f'井深: {depth}米\n权重: {weight*100:.1f}%' 
              for depth, weight in zip(result_df['井深'], result_df['分配权重'])]
    sizes = result_df['分配权重']
    
    # 设置颜色
    colors = plt.cm.tab20c(np.linspace(0, 1, len(sizes)))
    
    # 绘制饼图
    wedges, texts, autotexts = ax2.pie(sizes, labels=labels, autopct='%1.1f%%', 
                                      startangle=90, colors=colors,
                                      wedgeprops={'edgecolor': 'w', 'linewidth': 1},
                                      textprops={'fontsize': 10})
    
    ax2.set_title('分配权重分布', fontsize=14)
    ax2.axis('equal')  # 保证饼图是圆形
    
    # 调整布局
    plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为标题留出空间
    
    # 保存或显示图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"图片已保存至: {save_path}")
    
    plt.show()
    
    return fig

def plot_parameter_distribution(result_df, save_path=None):
    """
    可视化参数分布
    
    参数:
    result_df : DataFrame - 包含分配结果的数据框
    save_path : str - 图片保存路径（可选）
    """
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建图表
    fig, axs = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('参数分布分析', fontsize=16)
    
    # 1. 分配值与井深的关系
    axs[0, 0].plot(result_df['井深'], result_df['分配值'], 'o-', markersize=8, linewidth=2)
    axs[0, 0].set_title('分配值随井深变化', fontsize=14)
    axs[0, 0].set_xlabel('井深 (米)', fontsize=12)
    axs[0, 0].set_ylabel('分配值', fontsize=12)
    axs[0, 0].grid(True, linestyle='--', alpha=0.7)
    
    # 2. 分配权重与内摩擦角的关系
    axs[0, 1].scatter(result_df['内摩擦角'], result_df['分配权重'], 
                     s=result_df['分配权重']*5000, c=result_df['井深'], cmap='viridis', alpha=0.7)
    axs[0, 1].set_title('分配权重与内摩擦角', fontsize=14)
    axs[0, 1].set_xlabel('内摩擦角 (度)', fontsize=12)
    axs[0, 1].set_ylabel('分配权重', fontsize=12)
    axs[0, 1].grid(True, linestyle='--', alpha=0.7)
    
    # 添加颜色条
    cbar = plt.colorbar(axs[0, 1].collections[0], ax=axs[0, 1])
    cbar.set_label('井深 (米)', fontsize=12)
    
    # 3. 弹性模量与分配值的关系
    axs[1, 0].bar(result_df['井深'].astype(str), result_df['弹性模量'], 
                 color='skyblue', alpha=0.7, label='弹性模量')
    axs[1, 0].set_title('弹性模量与分配值对比', fontsize=14)
    axs[1, 0].set_xlabel('井深 (米)', fontsize=12)
    axs[1, 0].set_ylabel('弹性模量 (MPa)', fontsize=12)
    
    # 添加分配值的折线图
    ax2 = axs[1, 0].twinx()
    ax2.plot(result_df['井深'].astype(str), result_df['分配值'], 'ro-', markersize=8, linewidth=2, label='分配值')
    ax2.set_ylabel('分配值', fontsize=12)
    
    # 合并图例
    lines, labels = axs[1, 0].get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    axs[1, 0].legend(lines + lines2, labels + labels2, loc='upper left')
    
    # 4. M(σ₃)与分配权重的关系
    axs[1, 1].scatter(result_df['M(σ₃)'], result_df['分配权重'], 
                     c=result_df['内聚力'], s=100, cmap='coolwarm', alpha=0.8)
    axs[1, 1].set_title('M(σ₃)与分配权重关系', fontsize=14)
    axs[1, 1].set_xlabel('M(σ₃)', fontsize=12)
    axs[1, 1].set_ylabel('分配权重', fontsize=12)
    axs[1, 1].grid(True, linestyle='--', alpha=0.7)
    
    # 添加颜色条
    cbar = plt.colorbar(axs[1, 1].collections[0], ax=axs[1, 1])
    cbar.set_label('内聚力 (MPa)', fontsize=12)
    
    # 调整布局
    plt.tight_layout(rect=[0, 0, 1, 0.95])  # 为标题留出空间
    
    # 保存或显示图片
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"参数分布图片已保存至: {save_path}")
    
    plt.show()
    
    return fig

# ======================= 使用示例 =======================
if __name__ == "__main__":
    # 示例数据 (m行 - 源数据)
    data_m = {
        'q_tau': [500],       # 待分配的总值
        '实时密度': [1.25]     # g/cm³
    }
    df_m = pd.DataFrame(data_m)
    
    # 示例数据 (n行 - 目标数据)
    data_n = {
        '井深': [1000, 1500, 2000, 2500, 3000, 3500, 4000],           # 米
        '压力系数': [0.8, 0.82, 0.85, 0.88, 0.9, 0.92, 0.95],           # 无单位
        '内聚力': [15, 16, 18, 20, 22, 24, 26],                   # MPa
        '内摩擦角': [28, 30, 32, 34, 36, 38, 40],                 # 度
        '弹性模量': [18000, 20000, 22000, 24000, 26000, 28000, 30000]     # MPa
    }
    df_n = pd.DataFrame(data_n)
    
    # 计算分配
    result_df, q_tau_total = calculate_allocations(df_m, df_n)
    
    # 打印结果
    print("分配结果:")
    print(result_df)
    
    # 验证分配总和
    total_allocated = result_df['分配值'].sum()
    print(f"\n分配总和: {total_allocated:.2f} (原值: {data_m['q_tau'][0]})")
    
    # 可视化结果
    plot_allocation_results(result_df, q_tau_total, save_path='allocation_results.png')
    
    # 参数分布分析
    plot_parameter_distribution(result_df, save_path='parameter_distribution.png')