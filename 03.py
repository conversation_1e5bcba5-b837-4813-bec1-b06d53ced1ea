import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

def calculate_eta_and_M(df):
    """
    根据给定公式计算η和M(σ₃)
    
    公式:
    η = (2E * q(τ)) / M²(σ₃)
    M(σ₃) = ((1+sinφ)/(1-sinφ)) * σ₃ + (2τ₀cosφ)/(1-sinφ)
    σ₃ = P_w - P_o
    """
    results = []
    
    for idx, row in df.iterrows():
        depth = row['井深']
        pressure_coeff = row['压力系数']
        cohesion = row['内聚力']  # τ₀
        friction_angle = row['内摩擦角']  # φ (度)
        elastic_modulus = row['弹性模量']  # E
        density = row['实时密度']
        q_tau = row['q_tau']  # q(τ)
        
        # 计算σ₃ = P_w - P_o
        P_w = depth * density  # 水压
        P_o = pressure_coeff * depth  # 油压
        sigma_3 = P_w - P_o
        
        # 将内摩擦角转换为弧度
        phi_rad = np.radians(friction_angle)
        sin_phi = np.sin(phi_rad)
        cos_phi = np.cos(phi_rad)
        
        # 计算M(σ₃)
        denominator = 1 - sin_phi
        if abs(denominator) < 1e-10:
            M_sigma = 0.0
        else:
            term1 = ((1 + sin_phi) / denominator) * sigma_3
            term2 = (2 * cohesion * cos_phi) / denominator
            M_sigma = term1 + term2
        
        # 计算η
        if abs(M_sigma) < 1e-10:
            eta = 0.0
        else:
            eta = (2 * elastic_modulus * q_tau) / (M_sigma ** 2)
        
        results.append({
            '井深': depth,
            'σ₃': sigma_3,
            'M(σ₃)': M_sigma,
            'η': eta,
            '压力系数': pressure_coeff,
            '内聚力': cohesion,
            '内摩擦角': friction_angle,
            '弹性模量': elastic_modulus,
            'q(τ)': q_tau
        })
    
    return pd.DataFrame(results)

def plot_results(df):
    """
    绘制计算结果的图形展示
    """
    # 设置中文宋体和西文新罗马字体，字号小四(12pt)
    plt.rcParams['font.sans-serif'] = ['SimSun', 'Times New Roman']
    plt.rcParams['font.serif'] = ['SimSun', 'Times New Roman']
    plt.rcParams['font.family'] = ['serif']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.size'] = 12  # 小四字号
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('η 和 M(σ₃) 计算结果可视化', fontsize=14)
    
    # 1. η值随井深变化
    ax1.plot(df['井深'], df['η'], 'bo-', linewidth=2, markersize=8)
    ax1.set_title('η值随井深变化', fontsize=12)
    ax1.set_xlabel('井深 (米)', fontsize=12)
    ax1.set_ylabel('η', fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # 2. M(σ₃)值随井深变化
    ax2.plot(df['井深'], df['M(σ₃)'], 'ro-', linewidth=2, markersize=8)
    ax2.set_title('M(σ₃)值随井深变化', fontsize=12)
    ax2.set_xlabel('井深 (米)', fontsize=12)
    ax2.set_ylabel('M(σ₃)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    # 3. σ₃值随井深变化
    ax3.plot(df['井深'], df['σ₃'], 'go-', linewidth=2, markersize=8)
    ax3.set_title('σ₃值随井深变化', fontsize=12)
    ax3.set_xlabel('井深 (米)', fontsize=12)
    ax3.set_ylabel('σ₃', fontsize=12)
    ax3.grid(True, alpha=0.3)
    
    # 4. η与M(σ₃)的关系散点图
    scatter = ax4.scatter(df['M(σ₃)'], df['η'], c=df['井深'], 
                         s=100, cmap='viridis', alpha=0.7)
    ax4.set_title('η与M(σ₃)关系', fontsize=12)
    ax4.set_xlabel('M(σ₃)', fontsize=12)
    ax4.set_ylabel('η', fontsize=12)
    ax4.grid(True, alpha=0.3)
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax4)
    cbar.set_label('井深 (米)', fontsize=12)
    
    plt.tight_layout()
    plt.show()

def main():
    """
    主函数
    """
    # 读取数据
    try:
        df = pd.read_csv('data.csv')
        print("成功读取数据:")
        print(df.head())
        print()
    except FileNotFoundError:
        print("错误: 找不到data.csv文件")
        return
    
    # 计算η和M(σ₃)
    results_df = calculate_eta_and_M(df)
    
    # 显示计算结果
    print("计算结果:")
    print(results_df[['井深', 'σ₃', 'M(σ₃)', 'η']].round(4))
    print()
    
    # 统计信息
    print("统计信息:")
    print(f"η的平均值: {results_df['η'].mean():.4f}")
    print(f"η的最大值: {results_df['η'].max():.4f}")
    print(f"η的最小值: {results_df['η'].min():.4f}")
    print(f"M(σ₃)的平均值: {results_df['M(σ₃)'].mean():.4f}")
    print(f"M(σ₃)的最大值: {results_df['M(σ₃)'].max():.4f}")
    print(f"M(σ₃)的最小值: {results_df['M(σ₃)'].min():.4f}")
    
    # 绘制图形
    plot_results(results_df)
    
    # 保存结果到CSV
    results_df.to_csv('results.csv', index=False, encoding='utf-8-sig')
    print("\n结果已保存到 results.csv")

if __name__ == "__main__":
    main()
