import pandas as pd
import numpy as np
import math

def calculate_allocations(df_m, df_n):
    """
    将df_m中的q_tau值按照物理模型分配给df_n的每一行
    
    参数:
    df_m : DataFrame - 源数据 (m行)，包含列: ['q_tau', '实时密度']
    df_n : DataFrame - 目标数据 (n行)，包含列: ['井深', '压力系数', '内聚力', '内摩擦角', '弹性模量']
    
    返回:
    DataFrame - 添加了分配值的新df_n
    """
    # 复制数据避免修改原始DataFrame
    df_n = df_n.copy()
    
    # 验证输入数据
    required_m_columns = ['q_tau', '实时密度']
    required_n_columns = ['井深', '压力系数', '内聚力', '内摩擦角', '弹性模量']
    
    for col in required_m_columns:
        if col not in df_m.columns:
            raise ValueError(f"df_m缺少必要列: {col}")
    
    for col in required_n_columns:
        if col not in df_n.columns:
            raise ValueError(f"df_n缺少必要列: {col}")
    
    if len(df_m) != 1:
        raise ValueError("df_m应该只包含一行数据")
    
    # 提取m行数据
    q_tau_total = df_m['q_tau'].iloc[0]
    density_m = df_m['实时密度'].iloc[0]
    
    # 预计算中间变量
    k_values = []  # 存储每行的k值
    
    # 遍历df_n的每一行
    for idx, row in df_n.iterrows():
        depth = row['井深']
        pressure_coeff = row['压力系数']
        cohesion = row['内聚力']
        friction_angle = row['内摩擦角']
        elastic_modulus = row['弹性模量']
        
        # 计算σ₃ = P_w - P_o
        P_w = depth * density_m     # 水压
        P_o = pressure_coeff * depth  # 油压
        sigma_3 = P_w - P_o
        
        # 将内摩擦角转换为弧度
        phi_rad = np.radians(friction_angle)
        sin_phi = np.sin(phi_rad)
        cos_phi = np.cos(phi_rad)
        
        # 计算M(σ₃)
        numerator = 1 + sin_phi
        denominator = 1 - sin_phi
        
        # 避免除零错误
        if abs(denominator) < 1e-10:
            M_sigma = 0.0
        else:
            term1 = (numerator / denominator) * sigma_3
            term2 = (2 * cohesion * cos_phi) / denominator
            M_sigma = term1 + term2
        
        # 计算k值
        if abs(M_sigma) < 1e-10:
            k = 0.0
        else:
            k = (2 * elastic_modulus) / (M_sigma ** 2)
        
        k_values.append(k)
    
    # 计算归一化权重
    total_k = sum(k_values)
    
    # 分配q_tau值
    if total_k > 0:
        allocations = [(k / total_k) * q_tau_total for k in k_values]
    else:
        allocations = [0] * len(df_n)
    
    # 添加分配结果到df_n
    df_n['分配值'] = allocations
    df_n['分配权重'] = [k / total_k if total_k > 0 else 0 for k in k_values]
    
    return df_n

# ======================= 使用示例 =======================
if __name__ == "__main__":
    # 示例数据 (m行 - 源数据)
    data_m = {
        'q_tau': [500],       # 待分配的总值
        '实时密度': [1.25]     # g/cm³
    }
    df_m = pd.DataFrame(data_m)
    
    # 示例数据 (n行 - 目标数据)
    data_n = {
        '井深': [1000, 1500, 2000, 2500],           # 米
        '压力系数': [0.8, 0.85, 0.9, 0.95],           # 无单位
        '内聚力': [15, 18, 20, 22],                   # MPa
        '内摩擦角': [30, 32, 35, 38],                 # 度
        '弹性模量': [20000, 22000, 25000, 28000]     # MPa
    }
    df_n = pd.DataFrame(data_n)
    
    # 计算分配
    result_df = calculate_allocations(df_m, df_n)
    
    # 打印结果
    print("分配结果:")
    print(result_df)
    
    # 验证分配总和
    total_allocated = result_df['分配值'].sum()
    print(f"\n分配总和: {total_allocated:.2f} (原值: {data_m['q_tau'][0]})")